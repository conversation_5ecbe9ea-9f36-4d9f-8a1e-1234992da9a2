import { createTheme, alpha } from '@mui/material/styles';
import {
  extendedShadows,
  priorityColors,
  statusColors,
  extendedTypography,
  animations
} from './styles/themeExtensions';
import { applyVariantsToTheme } from './styles/themeVariants';

// Declare module augmentation for custom theme properties
declare module '@mui/material/styles' {
  interface Theme {
    custom: {
      priority: typeof priorityColors;
      status: typeof statusColors;
      shadows: typeof extendedShadows;
      animations: typeof animations;
    };
  }

  interface ThemeOptions {
    custom?: {
      priority?: typeof priorityColors;
      status?: typeof statusColors;
      shadows?: typeof extendedShadows;
      animations?: typeof animations;
    };
  }
}

const baseTheme = createTheme({
  palette: {
    primary: {
      main: '#3B82F6',
      light: '#93C5FD',
      dark: '#1E40AF',
    },
    secondary: {
      main: '#8B5CF6',
      light: '#C4B5FD',
      dark: '#7C3AED',
    },
    background: {
      default: '#F8FAFC',
      paper: '#ffffff',
    },
    text: {
      primary: '#1E293B',
      secondary: '#64748B',
    },
    success: {
      main: '#10B981',
      light: '#6EE7B7',
      dark: '#047857',
    },
    warning: {
      main: '#F59E0B',
      light: '#FCD34D',
      dark: '#D97706',
    },
    error: {
      main: '#EF4444',
      light: '#FCA5A5',
      dark: '#DC2626',
    },
    info: {
      main: '#3B82F6',
      light: '#93C5FD',
      dark: '#1E40AF',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
      lineHeight: 1.2,
    },
    h2: {
      fontWeight: 700,
      fontSize: '2rem',
      lineHeight: 1.3,
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.75rem',
      lineHeight: 1.3,
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.4,
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.25rem',
      lineHeight: 1.4,
    },
    h6: {
      fontWeight: 500,
      fontSize: '1.125rem',
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    caption: {
      fontSize: '0.75rem',
      lineHeight: 1.4,
    },
  },
  shape: {
    borderRadius: 8,
  },
  spacing: 8,
  transitions: {
    duration: {
      shortest: 150,
      shorter: 200,
      short: 250,
      standard: 300,
      complex: 375,
      enteringScreen: 225,
      leavingScreen: 195,
    },
    easing: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
    },
  },
  custom: {
    priority: priorityColors,
    status: statusColors,
    shadows: extendedShadows,
    animations,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: ({ theme }) => ({
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: theme.shape.borderRadius,
          transition: theme.transitions.create(['background-color', 'box-shadow', 'transform'], {
            duration: theme.transitions.duration.short,
          }),
          '&:hover': {
            transform: 'translateY(-1px)',
          },
        }),
        contained: ({ theme }) => ({
          boxShadow: extendedShadows.card,
          '&:hover': {
            boxShadow: extendedShadows.cardHover,
          },
        }),
      },
    },
    MuiCard: {
      styleOverrides: {
        root: ({ theme }) => ({
          boxShadow: extendedShadows.card,
          borderRadius: theme.shape.borderRadius,
          transition: theme.transitions.create(['box-shadow', 'transform'], {
            duration: theme.transitions.duration.short,
          }),
        }),
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: ({ theme }) => ({
          padding: theme.spacing(3),
          '&:last-child': {
            paddingBottom: theme.spacing(3),
          },
        }),
      },
    },
    MuiChip: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: theme.spacing(1),
          fontWeight: 500,
        }),
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderBottom: `1px solid ${theme.palette.divider}`,
        }),
        indicator: ({ theme }) => ({
          height: 3,
          borderRadius: '3px 3px 0 0',
        }),
      },
    },
    MuiTab: {
      styleOverrides: {
        root: ({ theme }) => ({
          textTransform: 'none',
          fontWeight: 500,
          fontSize: '0.875rem',
          minHeight: 48,
          transition: theme.transitions.create(['color', 'background-color'], {
            duration: theme.transitions.duration.short,
          }),
          '&:hover': {
            backgroundColor: alpha(theme.palette.primary.main, 0.04),
          },
        }),
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: ({ theme }) => ({
          boxShadow: extendedShadows.card,
        }),
      },
    },
    MuiAvatar: {
      styleOverrides: {
        root: ({ theme }) => ({
          transition: theme.transitions.create(['transform'], {
            duration: theme.transitions.duration.short,
          }),
          '&:hover': {
            transform: 'scale(1.05)',
          },
        }),
      },
    },
    MuiList: {
      styleOverrides: {
        root: ({ theme }) => ({
          padding: 0,
        }),
      },
    },
    MuiListItem: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: theme.shape.borderRadius,
          marginBottom: theme.spacing(0.5),
          transition: theme.transitions.create(['background-color'], {
            duration: theme.transitions.duration.short,
          }),
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
          '&:last-child': {
            marginBottom: 0,
          },
        }),
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: theme.shape.borderRadius,
          '&.Mui-selected': {
            backgroundColor: alpha(theme.palette.primary.main, 0.12),
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.16),
            },
          },
        }),
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: ({ theme }) => ({
          '& .MuiOutlinedInput-root': {
            borderRadius: theme.shape.borderRadius,
            transition: theme.transitions.create(['border-color', 'box-shadow'], {
              duration: theme.transitions.duration.short,
            }),
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: alpha(theme.palette.primary.main, 0.5),
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderWidth: 2,
              boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.1)}`,
            },
          },
        }),
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: theme.shape.borderRadius,
          boxShadow: extendedShadows.card,
        }),
      },
    },
    MuiContainer: {
      styleOverrides: {
        root: ({ theme }) => ({
          paddingLeft: theme.spacing(2),
          paddingRight: theme.spacing(2),
          [theme.breakpoints.up('sm')]: {
            paddingLeft: theme.spacing(3),
            paddingRight: theme.spacing(3),
          },
        }),
      },
    },
    MuiGrid: {
      styleOverrides: {
        root: ({ theme }) => ({
          '&.MuiGrid-container': {
            margin: 0,
            width: '100%',
          },
        }),
      },
    },
    MuiTypography: {
      styleOverrides: {
        h1: ({ theme }) => ({
          background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        }),
        h2: ({ theme }) => ({
          color: theme.palette.text.primary,
          fontWeight: 700,
        }),
        h3: ({ theme }) => ({
          color: theme.palette.text.primary,
          fontWeight: 600,
        }),
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: theme.shape.borderRadius,
          transition: theme.transitions.create(['background-color', 'transform'], {
            duration: theme.transitions.duration.short,
          }),
          '&:hover': {
            transform: 'scale(1.05)',
          },
        }),
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: ({ theme }) => ({
          backgroundColor: theme.palette.grey[800],
          fontSize: theme.typography.caption.fontSize,
          borderRadius: theme.shape.borderRadius,
          boxShadow: extendedShadows.dropdown,
        }),
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundImage: 'none',
        }),
        elevation1: {
          boxShadow: extendedShadows.card,
        },
        elevation2: {
          boxShadow: extendedShadows.cardHover,
        },
        elevation4: {
          boxShadow: extendedShadows.cardElevated,
        },
        elevation8: {
          boxShadow: extendedShadows.dropdown,
        },
        elevation16: {
          boxShadow: extendedShadows.modal,
        },
      },
    },
  },
});

// Apply variants to the base theme
export const theme = applyVariantsToTheme(baseTheme);